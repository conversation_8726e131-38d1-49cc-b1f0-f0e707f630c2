# Storage System Documentation

The Saday Agent platform includes a comprehensive file storage system that supports multiple file types, access control levels, and permission management.

## Overview

The storage system is designed to handle various file types including:
- Images (JPG, PNG, GIF, SVG, etc.)
- Documents (PDF, DOCX, etc.)
- Code files (Python, JavaScript, Rust, etc.)
- Data files (CSV, JSON, YAML, etc.)
- Archives (ZIP, TAR, etc.)
- Markdown and HTML files

## Architecture

### Components

1. **Storage Handler** (`services/backend/src/handler/storage.rs`)
   - Handles HTTP requests for file operations
   - Manages multipart file uploads
   - Implements access control logic

2. **Database Layer** (`services/backend/src/db.rs`)
   - File metadata storage
   - Permission management
   - Access logging

3. **Models** (`services/backend/src/models.rs`)
   - File, FilePermission, FileAccessLog structs
   - FileType and FileAccessType enums
   - PermissionType enum

4. **DTOs** (`services/backend/src/dtos.rs`)
   - Request/response data structures
   - File information transfer objects

### Database Schema

The storage system uses several database tables:

#### Files Table
```sql
CREATE TABLE files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_type file_type NOT NULL,
    access_type file_access_type NOT NULL DEFAULT 'private',
    uploaded_by UUID REFERENCES users(id) ON DELETE SET NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    download_count INTEGER DEFAULT 0,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### File Permissions Table
```sql
CREATE TABLE file_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    file_id UUID NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    permission_type permission_type NOT NULL,
    granted_by UUID REFERENCES users(id) ON DELETE SET NULL,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### File Access Logs Table
```sql
CREATE TABLE file_access_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    file_id UUID NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    access_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Access Control

### Access Types

1. **Public** (`public`)
   - No authentication required
   - Anyone can access the file
   - Suitable for public assets, documentation

2. **Private** (`private`)
   - Only the uploader can access
   - Requires authentication
   - Can grant explicit permissions to other users

3. **Organization** (`organization`)
   - All organization members can access
   - Requires authentication and organization membership
   - Can grant explicit permissions to users outside the organization

### Permission Types

1. **Read** (`read`)
   - Can view and download the file
   - Basic access permission

2. **Write** (`write`)
   - Can modify file metadata
   - Can update file content (future feature)

3. **Delete** (`delete`)
   - Can delete the file
   - Includes read and write permissions

4. **Admin** (`admin`)
   - Can manage file permissions
   - Includes all other permissions

## File Processing

### Upload Process

1. **Validation**
   - Check file size (max 100MB)
   - Validate multipart form data

2. **File Processing**
   - Generate unique filename with UUID
   - Preserve original filename
   - Detect file type from extension and MIME type

3. **Storage**
   - Save file to disk in `uploads/` directory
   - Create database record with metadata

4. **Response**
   - Return file information including URLs
   - Include generated metadata

### File Type Detection

The system uses a two-tier approach for file type detection:

1. **MIME Type Detection** (primary)
2. **Extension-based Detection** (fallback)

### Metadata Generation

Files automatically include metadata:
- `size_bytes`: File size in bytes
- `type`: General category (image, text, etc.)
- Future: dimensions for images, line count for code files

## API Endpoints

### Public Endpoints

- `POST /api/storage/public/upload` - Upload public files
- `GET /api/storage/public/:file_id` - Download public files

### Private Endpoints (Authentication Required)

- `POST /api/storage/private/upload` - Upload private/organization files
- `GET /api/storage/private/:file_id` - Download private files
- `GET /api/storage/private/my-files` - List user's files
- `POST /api/storage/private/:file_id/permissions` - Manage file permissions

## Usage Examples

### Upload a Public File

```bash
curl -X POST \
  http://localhost:8001/api/storage/public/upload \
  -F "file=@document.pdf"
```

### Upload a Private File

```bash
curl -X POST \
  http://localhost:8001/api/storage/private/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@private_document.pdf"
```

### Download a File

```bash
# View inline
curl http://localhost:8001/api/storage/public/FILE_UUID

# Force download
curl http://localhost:8001/api/storage/public/FILE_UUID?download=true
```

### Grant File Permission

```bash
curl -X POST \
  http://localhost:8001/api/storage/private/FILE_UUID/permissions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "USER_UUID",
    "permission_type": "read",
    "expires_at": "2024-12-31T23:59:59Z"
  }'
```

## Security Considerations

1. **File Size Limits**: 100MB per file to prevent abuse
2. **Access Control**: Strict permission checking on all operations
3. **Audit Logging**: All file access is logged with user and IP information
4. **Unique Filenames**: UUIDs prevent filename conflicts and guessing
5. **MIME Type Validation**: Prevents malicious file uploads

## Future Enhancements

1. **File Versioning**: Track file history and allow rollbacks
2. **Thumbnail Generation**: Automatic thumbnails for images
3. **Content Indexing**: Full-text search for documents
4. **Virus Scanning**: Malware detection for uploaded files
5. **CDN Integration**: Distribute files via content delivery network
6. **Bulk Operations**: Upload/download multiple files at once
7. **File Sharing**: Generate temporary public links for private files
