# API Reference

This document provides a comprehensive reference for the Saday Agent API endpoints.

## Base URL

All API endpoints are prefixed with: `http://your-domain.com/api`

For local development: `http://localhost:8001/api`

## Authentication

Most endpoints require authentication via JW<PERSON> token. The token can be provided in two ways:

1. As a cookie (set automatically after login)
2. In the Authorization header: `Authorization: Bearer YOUR_JWT_TOKEN`

## Response Format

All API responses follow a consistent format:

### Success Response

```json
{
  "status": "success",
  "data": { ... } // or message: "Success message"
}
```

### Error Response

```json
{
  "status": "error",
  "message": "Error description"
}
```

## Authentication Endpoints

### Request OTP

Sends an OTP code to the specified email for login or registration.

- **URL**: `/auth/request-otp`
- **Method**: `POST`
- **Auth required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "userType": "New", // or "Existing"
      "message": "OTP sent to email"
    }
    ```

### Verify OTP

Verifies the OTP and authenticates the user.

- **URL**: `/auth/verify-otp`
- **Method**: `POST`
- **Auth required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "otp_code": "123456",
    "name": "John Doe", // Required only for new users
    "profile_data": { // Optional
      "date_of_birth": "1990-01-01",
      "why_lola": "meditation", // one of: "meditation", "journaling", "both"
      "interests": ["mindfulness", "yoga"],
      "preferences": {
        "theme": "dark",
        "notifications": true
      },
      "bio": "Mindfulness enthusiast",
      "location": "San Francisco, CA"
    }
  }
  ```
- **Success Response**:
  - **Code**: 200 OK (existing user) or 201 Created (new user)
  - **Content**:
    ```json
    {
      "status": "success",
      "token": "jwt_token_here",
      "userType": "New" // or "Existing"
    }
    ```

### Resend OTP

Resends the OTP to the specified email address.

- **URL**: `/auth/resend-otp`
- **Method**: `POST`
- **Auth required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "message": "OTP resent to email"
    }
    ```

## User Endpoints

### Get Current User

Returns the currently authenticated user's information.

- **URL**: `/users/me`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "user": {
          "id": "user_id",
          "name": "John Doe",
          "email": "<EMAIL>",
          "role": "user",
          "profile_data": {
            "date_of_birth": "1990-01-01",
            "why_lola": "meditation",
            "interests": ["mindfulness", "yoga"],
            "preferences": {
              "theme": "dark",
              "notifications": true
            },
            "bio": "Mindfulness enthusiast",
            "location": "San Francisco, CA"
          },
          "createdAt": "2023-01-01T00:00:00Z",
          "updatedAt": "2023-01-01T00:00:00Z"
        }
      }
    }
    ```

### Update User

Updates the current user's information.

- **URL**: `/users/me`
- **Method**: `PATCH`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "name": "New Name",
    "date_of_birth": "1990-01-01",
    "why_lola": "journaling"
  }
  ```
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "id": "user_id",
        "name": "New Name",
        "email": "<EMAIL>",
        "role": "user",
        "date_of_birth": "1990-01-01",
        "why_lola": "journaling",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    }
    ```

## Agent Endpoints

### Create Agent

Creates a new agent.

- **URL**: `/agents`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "name": "My Agent",
    "description": "Agent description",
    "configuration": {
      "model": "gpt-4",
      "temperature": 0.7,
      "tools": ["web-search", "calculator"]
    }
  }
  ```
- **Success Response**:
  - **Code**: 201 Created
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "id": "agent_id",
        "name": "My Agent",
        "description": "Agent description",
        "configuration": {
          "model": "gpt-4",
          "temperature": 0.7,
          "tools": ["web-search", "calculator"]
        },
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    }
    ```

### Get Agents

Returns a list of agents owned by the current user.

- **URL**: `/agents`
- **Method**: `GET`
- **Auth required**: Yes
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Number of items per page (default: 10)
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "agents": [
          {
            "id": "agent_id",
            "name": "My Agent",
            "description": "Agent description",
            "createdAt": "2023-01-01T00:00:00Z",
            "updatedAt": "2023-01-01T00:00:00Z"
          }
        ],
        "total": 1,
        "page": 1,
        "limit": 10,
        "totalPages": 1
      }
    }
    ```

### Get Agent

Returns a specific agent by ID.

- **URL**: `/agents/:id`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "id": "agent_id",
        "name": "My Agent",
        "description": "Agent description",
        "configuration": {
          "model": "gpt-4",
          "temperature": 0.7,
          "tools": ["web-search", "calculator"]
        },
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    }
    ```

## Chat Endpoints

### Create Chat

Creates a new chat session with an agent.

- **URL**: `/chats`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "agentId": "agent_id",
    "title": "Chat Title"
  }
  ```
- **Success Response**:
  - **Code**: 201 Created
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "id": "chat_id",
        "agentId": "agent_id",
        "title": "Chat Title",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    }
    ```

### Send Message

Sends a message to a chat session.

- **URL**: `/chats/:chatId/messages`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "content": "Hello, agent!"
  }
  ```
- **Success Response**:
  - **Code**: 201 Created
  - **Content**:
    ```json
    {
      "status": "success",
      "data": {
        "id": "message_id",
        "chatId": "chat_id",
        "role": "user",
        "content": "Hello, agent!",
        "createdAt": "2023-01-01T00:00:00Z"
      }
    }
    ```

## WebSocket API

The WebSocket API enables real-time communication for chat messages and agent actions.

### Connection

Connect to the WebSocket server:

```
ws://your-domain.com/ws?token=YOUR_JWT_TOKEN
```

### Message Format

Messages sent and received through the WebSocket connection follow this format:

```json
{
  "type": "message_type",
  "payload": {
    // Message-specific data
  }
}
```

### Message Types

#### Chat Message

```json
{
  "type": "chat_message",
  "payload": {
    "id": "message_id",
    "chatId": "chat_id",
    "role": "assistant",
    "content": "Hello, I'm your agent!",
    "createdAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Agent Action

```json
{
  "type": "agent_action",
  "payload": {
    "id": "action_id",
    "chatId": "chat_id",
    "agentId": "agent_id",
    "action": "web_search",
    "parameters": {
      "query": "search query"
    },
    "status": "in_progress",
    "createdAt": "2023-01-01T00:00:00Z"
  }
}
```

## Storage Endpoints

The storage system supports file uploads, downloads, and access control with three access levels:
- **Public**: No authentication required for access
- **Private**: Only the uploader can access
- **Organization**: Organization members can access

### Upload Public Files

Upload files that are publicly accessible without authentication.

- **URL**: `/storage/public/upload`
- **Method**: `POST`
- **Auth required**: No
- **Content-Type**: `multipart/form-data`
- **File size limit**: 100MB per file
- **Supported file types**: Images, PDFs, documents (DOCX), CSV, code files, markdown, HTML, archives
- **Request**: Multipart form with file fields
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "message": "Successfully uploaded 2 file(s)",
      "files": [
        {
          "id": "file_uuid",
          "filename": "unique_filename.jpg",
          "original_filename": "my_image.jpg",
          "file_size": 1024000,
          "mime_type": "image/jpeg",
          "file_type": "image",
          "access_type": "public",
          "url": "http://your-domain.com/api/storage/public/file_uuid",
          "download_url": "http://your-domain.com/api/storage/public/file_uuid?download=true",
          "metadata": {
            "size_bytes": 1024000,
            "type": "image"
          },
          "createdAt": "2023-01-01T00:00:00Z"
        }
      ]
    }
    ```

### Upload Private Files

Upload files with private or organization-level access control.

- **URL**: `/storage/private/upload`
- **Method**: `POST`
- **Auth required**: Yes
- **Content-Type**: `multipart/form-data`
- **File size limit**: 100MB per file
- **Access control**:
  - If user has a default organization: files are organization-accessible
  - Otherwise: files are private to the user
- **Request**: Multipart form with file fields
- **Success Response**:
  - **Code**: 200 OK
  - **Content**: Same format as public upload, but with `access_type` as "private" or "organization"

### Get Public File

Download or view a publicly accessible file.

- **URL**: `/storage/public/:file_id`
- **Method**: `GET`
- **Auth required**: No
- **Query Parameters**:
  - `download`: If present, forces file download instead of inline display
- **Success Response**:
  - **Code**: 200 OK
  - **Headers**:
    - `Content-Type`: File's MIME type
    - `Content-Length`: File size
    - `Content-Disposition`: inline or attachment based on download parameter
  - **Body**: File binary data

### Get Private File

Download or view a private or organization file.

- **URL**: `/storage/private/:file_id`
- **Method**: `GET`
- **Auth required**: Yes
- **Query Parameters**:
  - `download`: If present, forces file download instead of inline display
- **Access Control**:
  - **Private files**: Only the uploader or users with explicit permissions
  - **Organization files**: Organization members or users with explicit permissions
- **Success Response**: Same as public file endpoint

### Get User Files

List files uploaded by the current user.

- **URL**: `/storage/private/my-files`
- **Method**: `GET`
- **Auth required**: Yes
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Number of items per page (default: 10, max: 50)
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "files": [
        {
          "id": "file_uuid",
          "filename": "unique_filename.jpg",
          "original_filename": "my_image.jpg",
          "file_size": 1024000,
          "mime_type": "image/jpeg",
          "file_type": "image",
          "access_type": "private",
          "uploaded_by": "user_uuid",
          "organization_id": null,
          "metadata": {
            "size_bytes": 1024000,
            "type": "image"
          },
          "download_count": 5,
          "createdAt": "2023-01-01T00:00:00Z",
          "updatedAt": "2023-01-01T00:00:00Z"
        }
      ],
      "results": 25
    }
    ```

### Manage File Permissions

Grant specific permissions to users or organizations for a file.

- **URL**: `/storage/private/:file_id/permissions`
- **Method**: `POST`
- **Auth required**: Yes
- **Permission required**: File owner or admin permission on the file
- **Request Body**:
  ```json
  {
    "user_id": "user_uuid", // Optional: grant permission to specific user
    "organization_id": "org_uuid", // Optional: grant permission to organization
    "permission_type": "read", // "read", "write", "delete", or "admin"
    "expires_at": "2023-12-31T23:59:59Z" // Optional: permission expiration
  }
  ```
- **Note**: Either `user_id` or `organization_id` must be provided
- **Success Response**:
  - **Code**: 200 OK
  - **Content**:
    ```json
    {
      "status": "success",
      "message": "Permission granted successfully",
      "permissions": []
    }
    ```

## File Types and Detection

The system automatically detects file types based on filename extensions and MIME types:

### Supported File Types

| Type | Extensions | Description |
|------|------------|-------------|
| **image** | jpg, jpeg, png, gif, bmp, webp, svg, ico, tiff, tif | Image files |
| **pdf** | pdf | PDF documents |
| **document** | doc, docx, odt, rtf | Word processing documents |
| **csv** | csv, tsv | Comma/tab-separated values |
| **markdown** | md, markdown, mdown, mkd, mdx | Markdown files |
| **html** | html, htm, xhtml | HTML files |
| **code** | rs, py, js, ts, jsx, tsx, java, c, cpp, cs, php, rb, go, swift, kt, scala, sql, json, xml, yaml, yml, toml, ini, cfg, conf, env, dockerfile, makefile, txt, log, readme, license, changelog, todo | Source code and text files |
| **archive** | zip, rar, 7z, tar, gz, bz2, xz, tgz, tbz2, txz | Compressed archives |
| **other** | All other extensions | Miscellaneous files |

### File Metadata

Files automatically include metadata based on their type:
- **All files**: `size_bytes`, file type classification
- **Images**: `type: "image"` (future: dimensions)
- **Text/Code files**: `type: "text"` (future: line count, language detection)

## Error Codes

| Status Code | Description                                          |
|-------------|------------------------------------------------------|
| 400         | Bad Request - Invalid input data                     |
| 401         | Unauthorized - Authentication required               |
| 403         | Forbidden - Insufficient permissions                 |
| 404         | Not Found - Resource not found                       |
| 409         | Conflict - Resource already exists                   |
| 422         | Unprocessable Entity - Validation error              |
| 429         | Too Many Requests - Rate limit exceeded              |
| 500         | Internal Server Error - Server-side error            |
